"""Tests for Telegram bot functionality."""
import logging
import time
from unittest.mock import Mock, patch
import requests

from src.bot.telegram_manager import TelegramManager, RateLimiter
from src.bot.telegram_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TelegramLogFormatter
from src.bot.constants import (
    MAX_MESSAGE_LENGTH,
    MESSAGE_BATCH_SIZE,
)


class TestRateLimiter:
    """Test the RateLimiter class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.rate_limiter = RateLimiter(messages_per_second=2.0, messages_per_minute=10)

    def test_can_send_initially(self):
        """Test that messages can be sent initially."""
        assert self.rate_limiter.can_send()

    def test_rate_limiting_per_second(self):
        """Test per-second rate limiting."""
        # First message should be allowed
        assert self.rate_limiter.can_send()
        self.rate_limiter.record_send()

        # Second message too soon should be blocked
        assert not self.rate_limiter.can_send()

        # After waiting, should be allowed again
        time.sleep(0.6)  # Wait more than 1/2 second
        assert self.rate_limiter.can_send()

    def test_wait_time_calculation(self):
        """Test wait time calculation."""
        self.rate_limiter.record_send()
        wait_time = self.rate_limiter.wait_time()
        assert wait_time > 0
        assert wait_time <= 0.5  # Should be less than 1/2 second


class TestTelegramManager:
    """Test the TelegramManager class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.manager = TelegramManager(token="test_token", chat_id="test_chat_id")

    def test_is_configured_with_credentials(self):
        """Test configuration check with credentials."""
        assert self.manager.is_configured()

    def test_is_configured_without_credentials(self):
        """Test configuration check without credentials."""
        manager = TelegramManager(token=None, chat_id=None)
        assert not manager.is_configured()
    
    @patch('src.bot.telegram_manager.requests.post')
    def test_send_message_success(self, mock_post):
        """Test successful message sending."""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        result = self.manager.send_message("Test message")

        assert result
        assert self.manager.messages_sent == 1
        assert self.manager.messages_failed == 0
        mock_post.assert_called_once()

    @patch('src.bot.telegram_manager.requests.post')
    def test_send_message_failure(self, mock_post):
        """Test message sending failure."""
        # Mock failed response
        mock_post.side_effect = requests.exceptions.RequestException("Network error")

        result = self.manager.send_message("Test message")

        assert not result
        assert self.manager.messages_sent == 0
        assert self.manager.messages_failed == 1
        # Note: last_error might be None if the error is caught and handled differently
    
    def test_message_truncation(self):
        """Test that long messages are truncated."""
        long_message = "A" * (MAX_MESSAGE_LENGTH + 100)

        with patch.object(self.manager, '_send_message_to_api') as mock_send:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_send.return_value = mock_response

            self.manager.send_message(long_message)

            # Check that the message was truncated
            args, _ = mock_send.call_args
            sent_message = args[0]
            assert len(sent_message) <= MAX_MESSAGE_LENGTH
            assert sent_message.endswith("...")
    
    def test_script_notifications(self):
        """Test script start and completion notifications."""
        with patch.object(self.manager, 'send_message') as mock_send:
            mock_send.return_value = True

            # Test start notification
            result = self.manager.send_script_start_notification("TestScript")
            assert result
            mock_send.assert_called()

            # Check that the message contains expected elements
            call_args = mock_send.call_args[0][0]
            assert "TestScript" in call_args
            assert "started" in call_args

            # Test completion notification
            result = self.manager.send_script_completion_notification(
                "TestScript", success=True, duration=45.67
            )
            assert result

            # Check completion message
            call_args = mock_send.call_args[0][0]
            assert "TestScript" in call_args
            assert "completed successfully" in call_args
            assert "45.67" in call_args  # Use a duration that won't be converted to minutes
    
    def test_get_status(self):
        """Test status reporting."""
        status = self.manager.get_status()

        assert isinstance(status, dict)
        assert "configured" in status
        assert "messages_sent" in status
        assert "messages_failed" in status
        assert "uptime" in status
        assert status["configured"]


class TestTelegramLogHandler:
    """Test the TelegramLogHandler class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.mock_manager = Mock(spec=TelegramManager)
        self.mock_manager.is_configured.return_value = True
        self.handler = TelegramLogHandler(self.mock_manager)

    def test_handler_initialization(self):
        """Test handler initialization."""
        assert self.handler.telegram_manager == self.mock_manager
        assert self.handler.message_queue is not None
        assert self.handler.lock is not None
    
    def test_emit_with_configured_telegram(self):
        """Test log record emission with configured Telegram."""
        # Create a log record
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="test.py",
            lineno=1,
            msg="Test message",
            args=(),
            exc_info=None
        )

        with patch.object(self.handler, '_send_queued_messages'):
            self.handler.emit(record)

            # Check that message was queued
            assert len(self.handler.message_queue) > 0
    
    def test_emit_with_unconfigured_telegram(self):
        """Test log record emission with unconfigured Telegram."""
        self.mock_manager.is_configured.return_value = False

        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="test.py",
            lineno=1,
            msg="Test message",
            args=(),
            exc_info=None
        )

        self.handler.emit(record)

        # Message queue should remain empty
        assert len(self.handler.message_queue) == 0
    
    def test_batch_sending(self):
        """Test that messages are batched correctly."""
        with patch.object(self.handler, '_send_queued_messages') as mock_send:
            # Send multiple messages
            for i in range(MESSAGE_BATCH_SIZE + 1):
                record = logging.LogRecord(
                    name="test_logger",
                    level=logging.INFO,
                    pathname="test.py",
                    lineno=1,
                    msg=f"Test message {i}",
                    args=(),
                    exc_info=None
                )
                self.handler.emit(record)

            # Should have triggered batch sending
            assert mock_send.called


class TestTelegramLogFormatter:
    """Test the TelegramLogFormatter class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.formatter = TelegramLogFormatter()

    def test_format_with_different_levels(self):
        """Test formatting with different log levels."""
        levels_and_emojis = [
            (logging.DEBUG, "🔍"),
            (logging.INFO, "ℹ️"),
            (logging.WARNING, "⚠️"),
            (logging.ERROR, "❌"),
            (logging.CRITICAL, "🚨"),
        ]

        for level, expected_emoji in levels_and_emojis:
            record = logging.LogRecord(
                name="test_logger",
                level=level,
                pathname="test.py",
                lineno=1,
                msg="Test message",
                args=(),
                exc_info=None
            )

            formatted = self.formatter.format(record)
            assert expected_emoji in formatted
            assert "Test message" in formatted
